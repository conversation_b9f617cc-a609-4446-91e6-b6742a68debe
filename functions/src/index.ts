import * as admin from "firebase-admin";
import * as functions from "firebase-functions";

import { UserEntity } from "./types";
import { monitorTonTransactions } from "./ton-monitor";

admin.initializeApp();
const db = admin.firestore();

export const createUserRecord = functions.auth.user().onCreate(async (user) => {
  const userRecord: UserEntity = {
    id: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    role: "user",
    createdAt:
      admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
  };

  try {
    await db.collection("users").doc(user.uid).set(userRecord);
    console.log(`User record created for ${user.uid}`);
  } catch (error) {
    console.error("Error creating user record:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message || "Server error while creating user record."
    );
  }
});

// Example function for getting user profile
export const getUserProfile = functions.https.onCall(async (data, context) => {
  // Ensure the user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required to get user profile."
    );
  }

  try {
    const userDoc = await db.collection("users").doc(context.auth.uid).get();

    if (!userDoc.exists) {
      throw new functions.https.HttpsError(
        "not-found",
        "User profile not found."
      );
    }

    return userDoc.data();
  } catch (error) {
    console.error("Error getting user profile:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message || "Server error while getting user profile."
    );
  }
});

// TON blockchain monitoring cron job - runs every 15 seconds
export const tonTransactionMonitor = functions.pubsub
  .schedule("*/15 * * * * *") // Every 15 seconds
  .timeZone("UTC")
  .onRun(async (context) => {
    try {
      console.log(
        "TON transaction monitor triggered at:",
        new Date().toISOString()
      );
      await monitorTonTransactions();
      console.log("TON transaction monitor completed successfully");
    } catch (error) {
      console.error("TON transaction monitor failed:", error);
      // Don't throw error to prevent function retries
    }
  });

// Export balance management functions
export {
  getBalance,
  checkPurchaseEligibility,
  lockOrderFunds,
  unlockOrderFunds,
  completeOrder,
} from "./balance-functions";
