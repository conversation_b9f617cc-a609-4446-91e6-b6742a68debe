import * as admin from "firebase-admin";
import { UserEntity, UserBalance } from "./types";

const db = admin.firestore();

/**
 * Get user balance
 */
export async function getUserBalance(userId: string): Promise<UserBalance> {
  try {
    const userDoc = await db.collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      throw new Error(`User ${userId} not found`);
    }
    
    const userData = userDoc.data() as UserEntity;
    return userData.balance || { sum: 0, locked: 0 };
  } catch (error) {
    console.error(`Error getting balance for user ${userId}:`, error);
    throw error;
  }
}

/**
 * Update user balance atomically
 */
export async function updateUserBalance(
  userId: string, 
  amountChange: number, 
  lockedChange: number = 0
): Promise<UserBalance> {
  try {
    const userRef = db.collection('users').doc(userId);
    
    return await db.runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);
      
      if (!userDoc.exists) {
        throw new Error(`User ${userId} not found`);
      }
      
      const userData = userDoc.data() as UserEntity;
      const currentBalance: UserBalance = userData.balance || { sum: 0, locked: 0 };
      
      const newBalance: UserBalance = {
        sum: Math.max(0, currentBalance.sum + amountChange),
        locked: Math.max(0, currentBalance.locked + lockedChange)
      };
      
      // Validate that locked amount doesn't exceed total
      if (newBalance.locked > newBalance.sum) {
        throw new Error('Locked amount cannot exceed total balance');
      }
      
      transaction.update(userRef, { balance: newBalance });
      
      return newBalance;
    });
  } catch (error) {
    console.error(`Error updating balance for user ${userId}:`, error);
    throw error;
  }
}

/**
 * Add funds to user balance (for top-ups)
 */
export async function addFunds(userId: string, amount: number): Promise<UserBalance> {
  if (amount <= 0) {
    throw new Error('Amount must be positive');
  }
  
  return await updateUserBalance(userId, amount, 0);
}

/**
 * Lock funds for an order
 */
export async function lockFunds(userId: string, amount: number): Promise<UserBalance> {
  if (amount <= 0) {
    throw new Error('Amount must be positive');
  }
  
  return await updateUserBalance(userId, 0, amount);
}

/**
 * Unlock funds (cancel order)
 */
export async function unlockFunds(userId: string, amount: number): Promise<UserBalance> {
  if (amount <= 0) {
    throw new Error('Amount must be positive');
  }
  
  return await updateUserBalance(userId, 0, -amount);
}

/**
 * Spend locked funds (complete order)
 */
export async function spendLockedFunds(userId: string, amount: number): Promise<UserBalance> {
  if (amount <= 0) {
    throw new Error('Amount must be positive');
  }
  
  return await updateUserBalance(userId, -amount, -amount);
}

/**
 * Check if user has sufficient available balance
 */
export async function hasAvailableBalance(userId: string, amount: number): Promise<boolean> {
  try {
    const balance = await getUserBalance(userId);
    const availableBalance = balance.sum - balance.locked;
    return availableBalance >= amount;
  } catch (error) {
    console.error(`Error checking available balance for user ${userId}:`, error);
    return false;
  }
}
