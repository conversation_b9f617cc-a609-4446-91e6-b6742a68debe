import * as admin from "firebase-admin";
import fetch from "node-fetch";
import { addFunds } from "./balance-service";
import { getTxLookup, initializeTxLookup, updateTxLookup } from "./tx-lookup";
import { UserEntity } from "./types";

const db = admin.firestore();

interface TonTransaction {
  hash: string;
  lt: string;
  account: {
    address: string;
  };
  in_msg?: {
    source?: string;
    value: string;
    message?: string;
  };
  out_msgs?: Array<{
    destination?: string;
    value: string;
  }>;
}

interface TonApiResponse {
  ok: boolean;
  result: TonTransaction[];
}

/**
 * Get TON RPC URL based on environment
 */
function getTonRpcUrl(): string {
  const network = process.env.TON_NETWORK || "testnet";
  return network === "mainnet"
    ? process.env.TON_RPC_URL_MAINNET || "https://toncenter.com/api/v2/"
    : process.env.TON_RPC_URL_TESTNET ||
        "https://testnet.toncenter.com/api/v2/";
}

/**
 * Get marketplace wallet address
 */
function getMarketplaceWallet(): string {
  const wallet = process.env.TON_MARKETPLACE_WALLET;
  if (!wallet) {
    throw new Error("TON_MARKETPLACE_WALLET environment variable not set");
  }
  return wallet;
}

/**
 * Fetch transactions from TON blockchain
 */
async function fetchTonTransactions(
  address: string,
  fromLt?: string
): Promise<TonTransaction[]> {
  const baseUrl = getTonRpcUrl();
  const apiKey = process.env.TONCENTER_API_KEY;

  let url = `${baseUrl}getTransactions?address=${address}&limit=100&to_lt=0&archival=true`;

  if (fromLt && fromLt !== "0") {
    url += `&from_lt=${fromLt}`;
  }

  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };

  if (apiKey) {
    headers["X-API-Key"] = apiKey;
  }

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(
        `TON API error: ${response.status} ${response.statusText}`
      );
    }

    const data: TonApiResponse = await response.json();

    if (!data.ok) {
      throw new Error("TON API returned error response");
    }

    return data.result || [];
  } catch (error) {
    console.error("Error fetching TON transactions:", error);
    throw error;
  }
}

/**
 * Extract sender address and amount from transaction
 */
function extractTransactionInfo(
  tx: TonTransaction
): { sender: string; amount: number; message?: string } | null {
  if (!tx.in_msg || !tx.in_msg.source || !tx.in_msg.value) {
    return null;
  }

  const amount = parseInt(tx.in_msg.value) / 1000000000; // Convert from nanotons to TON

  if (amount <= 0) {
    return null;
  }

  return {
    sender: tx.in_msg.source,
    amount,
    message: tx.in_msg.message,
  };
}

/**
 * Find user by TON wallet address
 */
async function findUserByTonWallet(
  tonWalletAddress: string
): Promise<UserEntity | null> {
  try {
    const usersRef = db.collection("users");
    const query = usersRef.where("ton_wallet_address", "==", tonWalletAddress);
    const snapshot = await query.get();

    if (snapshot.empty) {
      return null;
    }

    const doc = snapshot.docs[0];
    return {
      id: doc.id,
      ...doc.data(),
    } as UserEntity;
  } catch (error) {
    console.error("Error finding user by TON wallet:", error);
    return null;
  }
}

/**
 * Update user balance using balance service
 */
async function updateUserBalance(
  userId: string,
  amount: number
): Promise<void> {
  try {
    await addFunds(userId, amount);
    console.log(`Updated balance for user ${userId}: +${amount} TON`);
  } catch (error) {
    console.error(`Error updating balance for user ${userId}:`, error);
    throw error;
  }
}

/**
 * Process new transactions and update user balances
 */
async function processTransactions(
  transactions: TonTransaction[]
): Promise<void> {
  console.log(`Processing ${transactions.length} transactions`);

  for (const tx of transactions) {
    try {
      const txInfo = extractTransactionInfo(tx);

      if (!txInfo) {
        continue; // Skip transactions without valid incoming messages
      }

      console.log(
        `Processing transaction: ${tx.hash}, sender: ${txInfo.sender}, amount: ${txInfo.amount} TON`
      );

      // Find user by sender address
      const user = await findUserByTonWallet(txInfo.sender);

      if (!user) {
        console.log(`No user found for wallet address: ${txInfo.sender}`);
        continue;
      }

      if (!user.tg_id) {
        console.log(`User ${user.id} has no tg_id, skipping balance update`);
        continue;
      }

      // Update user balance
      await updateUserBalance(user.id, txInfo.amount);

      console.log(
        `Successfully processed topup for user ${user.id} (${user.tg_id}): ${txInfo.amount} TON`
      );
    } catch (error) {
      console.error(`Error processing transaction ${tx.hash}:`, error);
      // Continue processing other transactions even if one fails
    }
  }
}

/**
 * Main monitoring function
 */
export async function monitorTonTransactions(): Promise<void> {
  try {
    console.log("Starting TON transaction monitoring...");

    // Initialize tx lookup if needed
    await initializeTxLookup();

    // Get last checked record
    const txLookup = await getTxLookup();
    const lastCheckedLt = txLookup?.last_checked_record_id || "0";

    console.log(`Last checked LT: ${lastCheckedLt}`);

    // Get marketplace wallet address
    const marketplaceWallet = getMarketplaceWallet();

    // Fetch new transactions
    const transactions = await fetchTonTransactions(
      marketplaceWallet,
      lastCheckedLt
    );

    if (transactions.length === 0) {
      console.log("No new transactions found");
      return;
    }

    // Sort transactions by logical time (oldest first)
    transactions.sort((a, b) => parseInt(a.lt) - parseInt(b.lt));

    // Process transactions
    await processTransactions(transactions);

    // Update last checked record ID to the latest transaction's LT
    if (transactions.length > 0) {
      const latestLt = transactions[transactions.length - 1].lt;
      await updateTxLookup(latestLt);
      console.log(`Updated last checked LT to: ${latestLt}`);
    }

    console.log("TON transaction monitoring completed successfully");
  } catch (error) {
    console.error("Error in TON transaction monitoring:", error);
    throw error;
  }
}
