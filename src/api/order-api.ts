import { OrderEntity } from "@/core.constants";
import { firestore } from "@/root-context";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  DocumentSnapshot,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  updateDoc,
  writeBatch,
} from "firebase/firestore";

const COLLECTION_NAME = "orders";

export const createOrder = async (
  orderData: Omit<OrderEntity, "id" | "createdAt" | "updatedAt">
) => {
  try {
    const docRef = await addDoc(collection(firestore, COLLECTION_NAME), {
      ...orderData,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return docRef.id;
  } catch (error) {
    console.error("Error creating order:", error);
    throw error;
  }
};

export const updateOrder = async (
  id: string,
  orderData: Partial<OrderEntity>
) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...orderData,
      updatedAt: new Date(),
    });
  } catch (error) {
    console.error("Error updating order:", error);
    throw error;
  }
};

export const deleteOrder = async (id: string) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error("Error deleting order:", error);
    throw error;
  }
};

export const getOrders = async (
  pageSize: number = 10,
  lastDoc?: DocumentSnapshot
) => {
  try {
    let q = query(
      collection(firestore, COLLECTION_NAME),
      orderBy("createdAt", "desc"),
      limit(pageSize)
    );

    if (lastDoc) {
      q = query(
        collection(firestore, COLLECTION_NAME),
        orderBy("createdAt", "desc"),
        startAfter(lastDoc),
        limit(pageSize)
      );
    }

    const snapshot = await getDocs(q);
    const orders: (OrderEntity & { id: string })[] = [];

    snapshot.forEach((doc) => {
      orders.push({ id: doc.id, ...doc.data() } as OrderEntity & {
        id: string;
      });
    });

    return {
      orders,
      lastDoc: snapshot.docs[snapshot.docs.length - 1],
      hasMore: snapshot.docs.length === pageSize,
    };
  } catch (error) {
    console.error("Error fetching orders:", error);
    throw error;
  }
};

export const clearAllOrders = async () => {
  try {
    const snapshot = await getDocs(collection(firestore, COLLECTION_NAME));
    const batch = writeBatch(firestore);

    snapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(`Deleted ${snapshot.docs.length} orders`);
  } catch (error) {
    console.error("Error clearing orders:", error);
    throw error;
  }
};

export const createBulkOrders = async (
  orders: Omit<OrderEntity, "id" | "createdAt" | "updatedAt">[]
) => {
  try {
    const batch = writeBatch(firestore);
    const orderCollection = collection(firestore, COLLECTION_NAME);

    orders.forEach((orderData) => {
      const docRef = doc(orderCollection);
      batch.set(docRef, {
        ...orderData,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    });

    await batch.commit();
    console.log(`Created ${orders.length} orders`);
  } catch (error) {
    console.error("Error creating bulk orders:", error);
    throw error;
  }
};
